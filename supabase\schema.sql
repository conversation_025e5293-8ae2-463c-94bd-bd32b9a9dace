-- Create custom types for roles and permissions
CREATE TYPE public.app_role AS <PERSON>NUM ('user', 'admin', 'catering_provider');
CREATE TYPE public.provider_role_type AS ENUM ('owner', 'staff');
CREATE TYPE public.app_permission AS ENUM (
  'dashboard.access',
  'users.read',
  'users.write',
  'users.delete',
  'settings.read',
  'settings.write',
  'services.create',
  'services.read',
  'services.update',
  'services.delete',
  'bookings.read',
  'bookings.update',
  'calendar.read',
  'messages.read',
  'messages.create',
  'reviews.read',
  'reviews.respond',
  'analytics.basic'
);

-- Create profiles table that extends the auth.users table
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create user_roles table to track user roles
CREATE TABLE public.user_roles (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role app_role NOT NULL,
  provider_role provider_role_type,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (user_id, role)
);

-- Create role_permissions table to define which permissions each role has
CREATE TABLE public.role_permissions (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  role app_role NOT NULL,
  permission app_permission NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (role, permission)
);

-- Create provider_role_permissions table to define permissions for catering provider sub-roles
CREATE TABLE public.provider_role_permissions (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  provider_role provider_role_type NOT NULL,
  permission app_permission NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (provider_role, permission)
);

-- Insert default role permissions
INSERT INTO public.role_permissions (role, permission) VALUES
  -- Admin permissions (all permissions)
  ('admin', 'dashboard.access'),
  ('admin', 'users.read'),
  ('admin', 'users.write'),
  ('admin', 'users.delete'),
  ('admin', 'settings.read'),
  ('admin', 'settings.write'),

  -- Catering provider permissions
  ('catering_provider', 'dashboard.access'),
  ('catering_provider', 'services.create'),
  ('catering_provider', 'services.read'),
  ('catering_provider', 'services.update'),
  ('catering_provider', 'services.delete'),
  ('catering_provider', 'bookings.read'),
  ('catering_provider', 'bookings.update'),
  ('catering_provider', 'calendar.read'),
  ('catering_provider', 'messages.read'),
  ('catering_provider', 'messages.create'),
  ('catering_provider', 'reviews.read'),
  ('catering_provider', 'reviews.respond'),
  ('catering_provider', 'analytics.basic'),

  -- Regular user permissions (limited access)
  ('user', 'dashboard.access');

-- Insert provider role permissions
INSERT INTO public.provider_role_permissions (provider_role, permission) VALUES
  -- Owner permissions (all catering_provider permissions)
  ('owner', 'dashboard.access'),
  ('owner', 'services.create'),
  ('owner', 'services.read'),
  ('owner', 'services.update'),
  ('owner', 'services.delete'),
  ('owner', 'bookings.read'),
  ('owner', 'bookings.update'),
  ('owner', 'calendar.read'),
  ('owner', 'messages.read'),
  ('owner', 'messages.create'),
  ('owner', 'reviews.read'),
  ('owner', 'reviews.respond'),
  ('owner', 'analytics.basic'),

  -- Staff permissions (limited permissions)
  ('staff', 'dashboard.access'),
  ('staff', 'bookings.read'),
  ('staff', 'calendar.read'),
  ('staff', 'messages.read');

-- Create function to check if a user has a specific permission
CREATE OR REPLACE FUNCTION public.has_permission(
  requested_permission app_permission
) RETURNS BOOLEAN AS $$
DECLARE
  bind_permissions INT;
  user_role public.app_role;
  user_provider_role public.provider_role_type;
BEGIN
  -- Get the user's role and provider_role from JWT claims
  SELECT (auth.jwt() ->> 'user_role')::public.app_role INTO user_role;
  SELECT (auth.jwt() ->> 'provider_role')::public.provider_role_type INTO user_provider_role;

  -- For catering_provider role, check provider sub-role permissions
  IF user_role = 'catering_provider' AND user_provider_role IS NOT NULL THEN
    -- Count matching permissions for the user's provider sub-role
    SELECT COUNT(*)
    INTO bind_permissions
    FROM public.provider_role_permissions
    WHERE provider_role_permissions.permission = requested_permission
      AND provider_role_permissions.provider_role = user_provider_role;
  ELSE
    -- For other roles, use the original logic
    SELECT COUNT(*)
    INTO bind_permissions
    FROM public.role_permissions
    WHERE role_permissions.permission = requested_permission
      AND role_permissions.role = user_role;
  END IF;

  RETURN bind_permissions > 0;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = 'public';

-- Create function to set user role in JWT claims
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  avatar_url TEXT;
BEGIN
  -- Check for avatar_url in user metadata (from OAuth providers)
  -- For Google, the avatar URL is in the user's metadata
  IF NEW.raw_user_meta_data->>'avatar_url' IS NOT NULL THEN
    avatar_url := NEW.raw_user_meta_data->>'avatar_url';
  -- For Google specifically, it might also be in the identity provider data
  ELSIF NEW.identities IS NOT NULL AND jsonb_array_length(NEW.identities) > 0 THEN
    -- Check if the identity is from Google
    IF NEW.identities[0]->>'provider' = 'google' THEN
      -- Try to get the picture URL from identity data
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    -- Check if the identity is from Facebook
    ELSIF NEW.identities[0]->>'provider' = 'facebook' THEN
      -- Try to get the picture URL from identity data
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    END IF;
  ELSE
    avatar_url := NULL;
  END IF;

  -- Insert into public.profiles
  INSERT INTO public.profiles (id, full_name, avatar_url, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.identities[0]->'identity_data'->>'full_name', NEW.identities[0]->'identity_data'->>'name'),
    avatar_url,
    NOW()
  );

  -- Assign default 'user' role
  INSERT INTO public.user_roles (user_id, role)
  VALUES (NEW.id, 'user');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to handle new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create custom access token hook to add role to JWT
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SET search_path = 'public'
AS $$
DECLARE
  claims JSONB;
  user_role public.app_role;
  user_provider_role public.provider_role_type;
BEGIN
  -- Fetch the user role and provider_role from the user_roles table
  SELECT role, provider_role INTO user_role, user_provider_role
  FROM public.user_roles
  WHERE user_id = (event->>'user_id')::UUID;

  claims := event->'claims';

  IF user_role IS NOT NULL THEN
    -- Set the role claim
    claims := jsonb_set(claims, '{user_role}', to_jsonb(user_role));

    -- Set the provider_role claim if it exists
    IF user_provider_role IS NOT NULL THEN
      claims := jsonb_set(claims, '{provider_role}', to_jsonb(user_provider_role));
    END IF;
  ELSE
    claims := jsonb_set(claims, '{user_role}', '"user"');
  END IF;

  -- Update the 'claims' object in the original event
  event := jsonb_set(event, '{claims}', claims);

  -- Return the modified event
  RETURN event;
END;
$$;

-- Set up RLS (Row Level Security) policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.provider_role_permissions ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

-- Allow admins to view all profiles
CREATE POLICY "Admins can view all profiles"
  ON public.profiles FOR SELECT
  USING (has_permission('users.read'));

-- Allow admins to update any profile
CREATE POLICY "Admins can update any profile"
  ON public.profiles FOR UPDATE
  USING (has_permission('users.write'));

-- User roles policies
CREATE POLICY "Users can view their own roles"
  ON public.user_roles FOR SELECT
  USING (auth.uid() = user_id);

-- Allow admins to view all user roles
CREATE POLICY "Admins can view all user roles"
  ON public.user_roles FOR SELECT
  USING (has_permission('users.read'));

-- Allow admins to manage user roles
CREATE POLICY "Admins can manage user roles"
  ON public.user_roles FOR ALL
  USING (has_permission('users.write'));

-- Role permissions policies
CREATE POLICY "All authenticated users can view role permissions"
  ON public.role_permissions FOR SELECT
  TO authenticated
  USING (true);

-- Allow admins to manage role permissions
CREATE POLICY "Admins can manage role permissions"
  ON public.role_permissions FOR ALL
  USING (has_permission('settings.write'));

-- Provider role permissions policies (consolidated for performance)
CREATE POLICY "provider_role_permissions_consolidated_select_policy"
  ON public.provider_role_permissions FOR SELECT
  TO authenticated
  USING (true);

-- Allow admins to manage provider role permissions (separate policies for write operations)
CREATE POLICY "provider_role_permissions_admin_insert_policy"
  ON public.provider_role_permissions FOR INSERT
  WITH CHECK ((select has_permission('settings.write'::app_permission)));

CREATE POLICY "provider_role_permissions_admin_update_policy"
  ON public.provider_role_permissions FOR UPDATE
  USING ((select has_permission('settings.write'::app_permission)))
  WITH CHECK ((select has_permission('settings.write'::app_permission)));

CREATE POLICY "provider_role_permissions_admin_delete_policy"
  ON public.provider_role_permissions FOR DELETE
  USING ((select has_permission('settings.write'::app_permission)));

-- Grant necessary permissions to supabase_auth_admin
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT ALL ON TABLE public.user_roles TO supabase_auth_admin;
GRANT ALL ON TABLE public.profiles TO supabase_auth_admin;
GRANT ALL ON TABLE public.role_permissions TO supabase_auth_admin;
GRANT ALL ON TABLE public.provider_role_permissions TO supabase_auth_admin;
