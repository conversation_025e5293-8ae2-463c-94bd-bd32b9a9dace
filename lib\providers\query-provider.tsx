"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState, ReactNode } from "react";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

export function QueryProvider({ children }: { children: ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 10 * 60 * 1000, // 10 minutes - increased from 1 minute
            gcTime: 30 * 60 * 1000, // 30 minutes - increased from 5 minutes
            retry: 1,
            refetchOnWindowFocus: false,
            refetchOnReconnect: false, // Disable refetch on reconnect
            refetchOnMount: false, // Only refetch if data is stale
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children} <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
