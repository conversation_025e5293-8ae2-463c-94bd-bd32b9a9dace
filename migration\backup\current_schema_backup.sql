-- ============================================================================
-- CATERINGHUB DATABASE SCHEMA BACKUP
-- Created: 2025-01-17
-- Purpose: Backup current schema before simplifying authentication system
-- ============================================================================

-- Current ENUM types
CREATE TYPE public.app_role AS ENUM ('user', 'admin', 'catering_provider');
CREATE TYPE public.provider_role_type AS ENUM ('owner', 'staff');

-- Current tables
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE public.user_roles (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role app_role NOT NULL,
  provider_role provider_role_type,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (user_id, role)
);

-- Current functions
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  avatar_url TEXT;
BEGIN
  -- Extract avatar URL from OAuth providers
  IF NEW.raw_user_meta_data->>'avatar_url' IS NOT NULL THEN
    avatar_url := NEW.raw_user_meta_data->>'avatar_url';
  ELSIF NEW.identities IS NOT NULL AND jsonb_array_length(NEW.identities) > 0 THEN
    IF NEW.identities[0]->>'provider' = 'google' THEN
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    ELSIF NEW.identities[0]->>'provider' = 'facebook' THEN
      avatar_url := NEW.identities[0]->'identity_data'->>'picture';
    END IF;
  ELSE
    avatar_url := NULL;
  END IF;

  -- Create profile
  INSERT INTO public.profiles (id, full_name, avatar_url, updated_at)
  VALUES (
    NEW.id,
    COALESCE(
      NEW.raw_user_meta_data->>'full_name',
      NEW.identities[0]->'identity_data'->>'full_name',
      NEW.identities[0]->'identity_data'->>'name'
    ),
    avatar_url,
    NOW()
  );

  -- Assign default admin role as requested
  INSERT INTO public.user_roles (user_id, role)
  VALUES (NEW.id, 'admin');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SET search_path = 'public'
AS $$
DECLARE
  claims jsonb;
  user_role public.app_role;
  provider_role_val public.provider_role_type;
BEGIN
  -- Get user role and provider role
  SELECT ur.role, ur.provider_role INTO user_role, provider_role_val
  FROM public.user_roles ur
  WHERE ur.user_id = (event->>'user_id')::uuid
  LIMIT 1;

  -- Set default claims
  claims := event->'claims';

  -- Add role to claims
  claims := jsonb_set(claims, '{user_role}', to_jsonb(COALESCE(user_role, 'user'::public.app_role)));

  -- Add provider role if exists
  IF provider_role_val IS NOT NULL THEN
    claims := jsonb_set(claims, '{provider_role}', to_jsonb(provider_role_val));
  END IF;

  -- Return the modified event
  RETURN jsonb_set(event, '{claims}', claims);
END;
$$;

-- Trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- RLS Policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can view their own roles"
  ON public.user_roles FOR SELECT
  USING (auth.uid() = user_id);

-- Grants
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook TO supabase_auth_admin;
GRANT ALL ON TABLE public.user_roles TO supabase_auth_admin;
GRANT ALL ON TABLE public.profiles TO supabase_auth_admin;